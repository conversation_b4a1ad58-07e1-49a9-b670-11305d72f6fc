# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.1].define(version: 2011_12_12_222310) do
  create_table "attachments", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.string "blob_key"
    t.string "file_name"
    t.integer "date"
    t.string "unique_permalink"
    t.string "file_type"
    t.string "file_file_name"
    t.string "file_content_type"
    t.integer "file_file_size"
    t.datetime "file_updated_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "file_guid"
    t.integer "user_id"
    t.text "file_meta"
  end

  create_table "links", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.integer "user_id"
    t.string "name"
    t.string "unique_permalink"
    t.string "url"
    t.string "preview_url"
    t.string "description"
    t.float "price", default: 1.0
    t.integer "number_of_paid_downloads", default: 0
    t.integer "number_of_downloads", default: 0
    t.integer "download_limit", default: 0
    t.integer "number_of_views", default: 0
    t.float "balance", default: 0.0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "owner"
    t.integer "length_of_exclusivity", default: 0
    t.integer "create_date"
    t.datetime "purchase_disabled_at"
    t.datetime "deleted_at"
    t.integer "preview_attachment_id"
    t.boolean "email_required", default: false
    t.integer "price_cents"
    t.index ["unique_permalink"], name: "index_links_on_unique_permalink"
  end

  create_table "purchases", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.integer "user_id"
    t.string "unique_permalink"
    t.float "price"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "owner"
    t.integer "create_date"
    t.integer "fee_cents"
    t.integer "link_id"
    t.text "email"
    t.integer "price_cents"
    t.index ["link_id"], name: "index_purchases_on_link_id"
  end

  create_table "receipts", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.text "email"
    t.integer "purchase_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["purchase_id"], name: "index_receipts_on_purchase_id"
  end

  create_table "users", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.string "email", default: ""
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.integer "sign_in_count", default: 0
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "name"
    t.string "payment_address"
    t.integer "create_date"
    t.float "balance", default: 0.0
    t.string "reset_hash"
    t.string "password_salt"
    t.datetime "confirmed_at"
    t.string "confirmation_token"
    t.datetime "confirmation_sent_at"
    t.string "unconfirmed_email"
    t.string "provider"
    t.integer "twitter_user_id"
    t.string "facebook_uid"
    t.integer "absorbed_to_user_id"
    t.datetime "deleted_at"
    t.index ["email"], name: "index_users_on_email"
    t.index ["facebook_uid"], name: "index_users_on_facebook_uid"
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
    t.index ["twitter_user_id"], name: "index_users_on_twitter_user_id"
  end

end
