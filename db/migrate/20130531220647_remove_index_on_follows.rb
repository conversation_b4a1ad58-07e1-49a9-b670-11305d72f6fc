# frozen_string_literal: true

class RemoveIndexOnFollows < ActiveRecord::Migration[7.1]
  def change
    if index_exists?(:follows, name: "index_follows_on_follower_id_and_followed_id")
      remove_index :follows, name: "index_follows_on_follower_id_and_followed_id"
    end
    if index_exists?(:follows, name: "index_follows_on_followed_id")
      remove_index :follows, name: "index_follows_on_followed_id"
    end
  end
end
