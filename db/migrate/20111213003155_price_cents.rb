# frozen_string_literal: true

class PriceCents < ActiveRecord::Migration[7.1]
  def up
    add_column :links, :price_cents, :integer unless column_exists?(:links, :price_cents)
    add_column :purchases, :price_cents, :integer unless column_exists?(:purchases, :price_cents)

    # Use raw SQL to avoid model dependencies that may not exist at this migration point
    execute "UPDATE links SET price_cents = ROUND(price * 100) WHERE price_cents IS NULL"
    execute "UPDATE purchases SET price_cents = ROUND(price * 100) WHERE price_cents IS NULL"
  end

  def down
    remove_column :links, :price_cents
    remove_column :purchases, :price_cents
  end
end
