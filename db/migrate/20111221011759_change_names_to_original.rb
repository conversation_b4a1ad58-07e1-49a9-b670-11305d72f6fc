# frozen_string_literal: true

class ChangeNamesToOriginal < ActiveRecord::Migration[7.1]
  def up
    # Use raw SQL to avoid model dependencies that may not exist at this migration point
    execute <<-SQL
      UPDATE attachments
      SET file_file_name = CONCAT('original', SUBSTRING(file_file_name, LOCATE('.', file_file_name)))
      WHERE file_file_name IS NOT NULL AND file_file_name LIKE '%.%'
    SQL
  end

  def down
  end
end
