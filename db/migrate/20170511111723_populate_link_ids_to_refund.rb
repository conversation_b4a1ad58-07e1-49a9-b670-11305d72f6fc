# frozen_string_literal: true

class PopulateLinkIdsToRefund < ActiveRecord::Migration[7.1]
  def change
    # Use raw SQL to avoid model dependencies
    execute <<-SQL
      UPDATE refunds
      SET link_id = (
        SELECT purchases.link_id
        FROM purchases
        WHERE purchases.id = refunds.purchase_id
      )
      WHERE refunds.link_id IS NULL
      AND refunds.purchase_id IS NOT NULL
    SQL
  end
end
