# frozen_string_literal: true

class CreatePayments < ActiveRecord::Migration[7.1]
  def change
    unless table_exists?(:payments)
      create_table :payments do |t|
        t.references :user
        t.string :status
        t.text :status_data
        t.float :amount

        t.timestamps
      end
    end

    unless index_exists?(:payments, :user_id)
      add_index :payments, :user_id
    end
  end
end
